package handler

import (
	"context"
	"encoding/json"
	"reflect"
	"strings"

	"gitlab.viettelcyber.com/awesome-threat/library/clock"
	"gitlab.viettelcyber.com/ti-micro/ws-threat/defs"
	"gitlab.viettelcyber.com/ti-micro/ws-threat/model"
)

func SaveHistory(
	ctx context.Context,
	docID, action, editor, description string,
	oldObj, newObj interface{},
	oldLang, newLang *model.CVELang,
	storeFunc func(context.Context, *model.History) error,
) error {
	excludedFields := map[string]bool{
		"Modified":   true,
		"Searchable": true,
		// Note: Score is NOT excluded anymore - we need to track VTI changes
	}

	oldData := make(map[string]interface{})
	newData := make(map[string]interface{})
	changedKeys := []string{}

	// Deep diff with parent object preservation
	deepDiffWithParent(oldObj, newObj, "", excludedFields, oldData, newData, &changedKeys)

	// Handle language changes
	if oldLang != nil && newLang != nil {
		handleLanguageChanges(oldLang, newLang, oldData, newData, &changedKeys)
	}

	// Special handling for VTI severity changes
	handleVTISeverityChanges(oldObj, newObj, oldData, newData, &changedKeys)

	if len(changedKeys) == 0 {
		return nil
	}

	oldJSON, _ := json.Marshal(oldData)
	newJSON, _ := json.Marshal(newData)
	now, _ := clock.Now(clock.Local)

	history := &model.History{
		Document:    docID,
		Editor:      editor,
		Description: description,
		Action:      action,
		OldData:     string(oldJSON),
		NewData:     string(newJSON),
		ChangedKeys: changedKeys,
		Created:     clock.UnixMilli(now),
		HistoryType: defs.HistoryTypeSystem,
	}
	history.GenID()
	return storeFunc(ctx, history)
}

func deepDiffWithParent(oldObj, newObj interface{}, prefix string, exclude map[string]bool, oldData, newData map[string]interface{}, changedKeys *[]string) {
	oldVal := reflect.ValueOf(oldObj)
	newVal := reflect.ValueOf(newObj)

	if oldVal.Kind() == reflect.Ptr {
		oldVal = oldVal.Elem()
	}
	if newVal.Kind() == reflect.Ptr {
		newVal = newVal.Elem()
	}

	if oldVal.Kind() != reflect.Struct || newVal.Kind() != reflect.Struct {
		return
	}

	typ := oldVal.Type()
	parentChanged := false
	parentOldData := make(map[string]interface{})
	parentNewData := make(map[string]interface{})

	for i := 0; i < oldVal.NumField(); i++ {
		field := typ.Field(i)
		if !field.IsExported() {
			continue
		}

		fieldName := field.Name
		if exclude[fieldName] {
			continue
		}

		fullName := fieldName
		if prefix != "" {
			fullName = prefix + "." + fieldName
		}

		oldField := oldVal.Field(i).Interface()
		newField := newVal.Field(i).Interface()

		// Always capture field data for parent object
		parentOldData[fieldName] = oldField
		parentNewData[fieldName] = newField

		// Handle nested structs
		if reflect.TypeOf(oldField).Kind() == reflect.Struct && reflect.TypeOf(newField).Kind() == reflect.Struct {
			// Recursively check nested struct
			nestedOldData := make(map[string]interface{})
			nestedNewData := make(map[string]interface{})
			nestedChangedKeys := []string{}
			
			deepDiffWithParent(oldField, newField, fullName, exclude, nestedOldData, nestedNewData, &nestedChangedKeys)
			
			// If nested struct has changes, mark parent as changed
			if len(nestedChangedKeys) > 0 {
				parentChanged = true
				// Add nested changes to main data
				for k, v := range nestedOldData {
					oldData[k] = v
				}
				for k, v := range nestedNewData {
					newData[k] = v
				}
				*changedKeys = append(*changedKeys, nestedChangedKeys...)
			}
			continue
		}

		// Direct field comparison
		if !reflect.DeepEqual(oldField, newField) {
			parentChanged = true
			*changedKeys = append(*changedKeys, fullName)
		}
	}

	// If any field in this struct changed, save the entire parent object
	if parentChanged {
		parentKey := "Root"
		if prefix != "" {
			parentKey = prefix
		}
		
		// Save entire parent object (including unchanged fields)
		oldData[parentKey] = parentOldData
		newData[parentKey] = parentNewData
		
		// Add parent key to changed keys if not already present
		found := false
		for _, key := range *changedKeys {
			if key == parentKey {
				found = true
				break
			}
		}
		if !found {
			*changedKeys = append(*changedKeys, parentKey)
		}
	}
}

func handleLanguageChanges(oldLang, newLang *model.CVELang, oldData, newData map[string]interface{}, changedKeys *[]string) {
	langChanged := false
	langOldData := make(map[string]interface{})
	langNewData := make(map[string]interface{})

	// Always capture all language data
	langOldData["Patch"] = oldLang.Patch
	langOldData["Description"] = oldLang.Description
	langOldData["Reference"] = oldLang.Reference
	
	langNewData["Patch"] = newLang.Patch
	langNewData["Description"] = newLang.Description
	langNewData["Reference"] = newLang.Reference

	// Check for changes
	if !reflect.DeepEqual(oldLang.Patch, newLang.Patch) {
		langChanged = true
		*changedKeys = append(*changedKeys, "Language.Patch")
	}
	if !reflect.DeepEqual(oldLang.Description, newLang.Description) {
		langChanged = true
		*changedKeys = append(*changedKeys, "Language.Description")
	}
	if !reflect.DeepEqual(oldLang.Reference, newLang.Reference) {
		langChanged = true
		*changedKeys = append(*changedKeys, "Language.Reference")
	}

	// If any language field changed, save entire language object
	if langChanged {
		oldData["Language"] = langOldData
		newData["Language"] = langNewData
		*changedKeys = append(*changedKeys, "Language")
	}
}

func handleVTISeverityChanges(oldObj, newObj interface{}, oldData, newData map[string]interface{}, changedKeys *[]string) {
	// Extract VTI severity from Score field
	oldVTISeverity := extractVTISeverity(oldObj)
	newVTISeverity := extractVTISeverity(newObj)

	if oldVTISeverity != newVTISeverity {
		// Save old and new VTI severity
		vtiOldData := map[string]interface{}{
			"Severity": oldVTISeverity,
		}
		vtiNewData := map[string]interface{}{
			"Severity": newVTISeverity,
		}

		oldData["VTI"] = vtiOldData
		newData["VTI"] = vtiNewData
		*changedKeys = append(*changedKeys, "VTI.Severity")
		*changedKeys = append(*changedKeys, "VTI")
	}
}

func extractVTISeverity(obj interface{}) string {
	val := reflect.ValueOf(obj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return ""
	}

	// Try to find Score.VTI.Severity field
	scoreField := val.FieldByName("Score")
	if !scoreField.IsValid() {
		return ""
	}

	if scoreField.Kind() == reflect.Ptr {
		scoreField = scoreField.Elem()
	}

	vtiField := scoreField.FieldByName("VTI")
	if !vtiField.IsValid() {
		return ""
	}

	if vtiField.Kind() == reflect.Ptr {
		vtiField = vtiField.Elem()
	}

	severityField := vtiField.FieldByName("Severity")
	if !severityField.IsValid() {
		return ""
	}

	if severityField.Kind() == reflect.String {
		return severityField.String()
	}

	return ""
}
