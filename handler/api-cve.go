package handler

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab.viettelcyber.com/ti-micro/ws-threat/utils"
	"io/ioutil"
	"log"
	"math"
	"net/http"
	"os"
	"regexp"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/k3a/html2text"
	"github.com/labstack/echo/v4"
	"github.com/panjf2000/ants/v2"
	es "gitlab.viettelcyber.com/awesome-threat/library/adapter/elastic"
	mg "gitlab.viettelcyber.com/awesome-threat/library/adapter/mongo"
	"gitlab.viettelcyber.com/awesome-threat/library/adapter/rabbit"
	"gitlab.viettelcyber.com/awesome-threat/library/adapter/redis"
	"gitlab.viettelcyber.com/awesome-threat/library/clock"
	"gitlab.viettelcyber.com/awesome-threat/library/core/cpe"
	"gitlab.viettelcyber.com/awesome-threat/library/hash"
	"gitlab.viettelcyber.com/awesome-threat/library/log/pencil"
	"gitlab.viettelcyber.com/awesome-threat/library/rest"
	"gitlab.viettelcyber.com/awesome-threat/library/slice"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/sync/errgroup"

	"gitlab.viettelcyber.com/ti-micro/ws-threat/adapter/elastic"
	"gitlab.viettelcyber.com/ti-micro/ws-threat/adapter/mongo"
	"gitlab.viettelcyber.com/ti-micro/ws-threat/defs"
	"gitlab.viettelcyber.com/ti-micro/ws-threat/model"
)

type CVEHandler struct {
	name         string
	logger       pencil.Logger
	elastic      elastic.GlobalRepository
	mongo        mongo.GlobalRepository
	queue        rabbit.Service
	cache        redis.Service
	assets       map[string][]*cpe.Item
	poolLanguage *ants.PoolWithFunc
	mutex        *sync.Mutex
	config       model.Config
	isTest       bool
}

func NewCVEHandler(config model.Config, isTest bool) CVEHandlerInterface {
	logger, _ := pencil.New(defs.HandlerCve, pencil.DebugLevel, true, os.Stdout)
	handler := &CVEHandler{
		name:    defs.HandlerCve,
		logger:  logger,
		elastic: elastic.NewGlobalRepository(config.Connector.Elastic),
		mongo:   mongo.NewGlobalRepository(config.Connector.Mongo),
		queue:   rabbit.NewService(config.Connector.Rabbit.Crawler, nil),
		cache:   redis.NewService(config.Connector.Redis.General, nil),
		assets:  map[string][]*cpe.Item{},
		mutex:   &sync.Mutex{},
		config:  config,
		isTest:  isTest,
	}
	handler.poolLanguage = handler.newPoolMultilang()
	handler.crawlAsset(context.Background())
	// Success
	return handler
}

func (h *CVEHandler) Config(c echo.Context) error {
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{
		"severity":       defs.MappingCveSeverity,
		"severity_range": defs.MappingCveSeverityRange,
		"status":         defs.MappingCveStatus,
		"checklist": map[string]interface{}{
			defs.LangEN: defs.MappingCveChecklistEN,
			defs.LangVI: defs.MappingCveChecklistVI,
		},
		"product_type": defs.MappingProductType,
		"cvss":         defs.MappingCvss,
		"language":     defs.MappingLanguageStatistic,
	}).Go()
}

func (h *CVEHandler) Identify(c echo.Context) error {
	editor := c.Get("user_name").(string)
	c.SetCookie(&http.Cookie{
		Name:   "username",
		Value:  editor,
		Domain: c.Request().Host,
	})
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(editor).Go()
}

func (h *CVEHandler) Search(c echo.Context) error {
	body, err := h.verifySearch(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}

	if body.Time.Approved.Gte > 0 || body.Time.Approved.Lte > 0 {
		if len(body.Status) > 1 || (len(body.Status) == 1 && body.Status[0] != 2) {
			return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": make([]interface{}, 0), "total": 0}).Go()
		}
	}

	query := body.PrepareQuery()
	results, err := h.elastic.Enrichment().CVE().Find(context.Background(), query, body.Sort, body.Offset, body.Size)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
		return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": make([]interface{}, 0), "total": 0}).Go()
	}
	response := make([]*model.CVEVerbose, 0)
	for _, result := range results {
		if result.Status != defs.StatusCodeApproved {
			result.Approved = 0
		}
		// change the global score for displaying
		gVer, isFilter := getGreatestVersionFilter(body.Severity.Global.Version)
		if isFilter {
			switch gVer {
			case defs.VersionCvssV4:
				result.Score.Global = result.Score.CVSS4
			case defs.VersionCvssV3:
				result.Score.Global = result.Score.CVSS3
			case defs.VersionCvssV2:
				result.Score.Global = result.Score.CVSS2
			}
		}
		response = append(response, &model.CVEVerbose{
			CVE: *result,
		})
	}
	wg := &sync.WaitGroup{}
	for idx, _ := range response {
		job := &model.CVEJobLanguage{
			WG:      wg,
			Index:   idx,
			Results: response,
		}
		wg.Add(1)
		if err = h.poolLanguage.Invoke(job); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}
	wg.Wait()
	count, err := h.elastic.Enrichment().CVE().Count(context.Background(), query)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": make([]interface{}, 0), "total": 0}).Go()
	}
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": response, "total": count}).Go()
}

func getGreatestVersionFilter(
	versions []string,
) (version string, useFilter bool) {
	if slices.Contains(versions, defs.VersionCvssV4) {
		return defs.VersionCvssV4, true
	} else if slices.Contains(versions, defs.VersionCvssV3) {
		return defs.VersionCvssV3, true
	} else if slices.Contains(versions, defs.VersionCvssV2) {
		return defs.VersionCvssV2, true
	}

	return "", false
}

func trimspaceReq(req model.RequestCVESearch) model.RequestCVESearch {
	versions := make([]string, 0)
	for _, it := range req.Severity.Global.Version {
		versions = append(versions, strings.TrimSpace(it))
	}
	return model.RequestCVESearch{
		Keyword: strings.TrimSpace(req.Keyword),
		Checker: strings.TrimSpace(req.Checker),
		Severity: model.RequestCVESeverity{
			VTI: model.RequestCVESeverityVerbose{
				Version: strings.TrimSpace(req.Severity.VTI.Version),
				Value:   req.Severity.VTI.Value,
			},
			Global: model.RequestCVESeverityVerboseV2{
				Version:          versions,
				SeverityVersion2: req.Severity.Global.SeverityVersion2,
				SeverityVersion3: req.Severity.Global.SeverityVersion3,
			},
		},
		Status:    req.Status,
		Languages: req.Languages,
		Time:      req.Time,
		Sort:      req.Sort,
		Size:      req.Size,
		Offset:    req.Offset,
	}
}

func (h *CVEHandler) ExportListCve(c echo.Context) error {
	body, err := h.verifyExport(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}

	body.Ids = validateListID(body.Ids)
	cveResp := make([]*model.CVE, 0)
	if len(body.Ids) > 0 {
		var (
			wg       sync.WaitGroup
			wg1      sync.WaitGroup
			poolSize int = h.config.NumberWorker
		)
		type results struct {
			err   error
			value *model.CVE
		}

		result := make(chan results, 10)
		var err1 error
		wg1.Add(1)
		go func() {
			defer wg1.Done()
			for value := range result {
				if value.err != nil {
					if err.Error() != es.NotFoundError {
						err1 = value.err
					}
				}
				if value.value != nil {
					cveResp = append(cveResp, value.value)
				}
			}
		}()

		pool, _ := ants.NewPoolWithFunc(poolSize, func(i interface{}) {
			defer wg.Done()
			document, _ := h.elastic.Enrichment().CVE().GetByID(context.Background(), i.(string))

			rs := results{
				err:   err,
				value: document,
			}
			result <- rs
		})
		defer pool.Release()
		for _, id := range body.Ids {
			wg.Add(1)
			if err := pool.Invoke(id); err != nil {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
		}

		wg.Wait()
		close(result)
		wg1.Wait()
		if err1 != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}

	} else {
		body.Req = trimspaceReq(body.Req)
		if body.Req.Time.Approved.Gte > 0 || body.Req.Time.Approved.Lte > 0 {
			if len(body.Req.Status) > 1 || (len(body.Req.Status) == 1 && body.Req.Status[0] != 2) {
				return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": make([]interface{}, 0), "total": 0}).Go()
			}
		}
		query := body.Req.PrepareQuery()
		cveResp, err = h.elastic.Enrichment().CVE().Find(context.Background(), query, body.Req.Sort, body.Req.Offset, body.Req.Size)
		if err != nil {
			if err.Error() != es.NotFoundError {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
		}

		// change the global score for displaying
		for _, cve := range cveResp {
			gVer, isFilter := getGreatestVersionFilter(
				body.Req.Severity.Global.Version)
			if isFilter {
				switch gVer {
				case defs.VersionCvssV4:
					cve.Score.Global = cve.Score.CVSS4
				case defs.VersionCvssV3:
					cve.Score.Global = cve.Score.CVSS3
				case defs.VersionCvssV2:
					cve.Score.Global = cve.Score.CVSS2
				}
			}
		}
	}

	if len(cveResp) == 0 {
		return c.JSON(http.StatusNotFound, model.ResponseNodata{
			Success: false,
			Message: "No data",
			Detail:  nil,
		})
	}

	exportExcel := getExportExcelData(cveResp, body.Req, defs.LangEN)
	path := ""
	if h.isTest {
		path = ".."
	}
	err = h.ExportListExcelCVE(c, exportExcel, defs.LangEN, h.isTest, path)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	return nil
}

func validateListID(ids []string) []string {
	result := make([]string, 0)
	if len(ids) == 0 {
		return result
	}
	for _, v := range ids {
		if len(strings.TrimSpace(v)) != 0 {
			result = append(result, strings.TrimSpace(v))
		}
	}
	return result
}

func (h *CVEHandler) Statistic(c echo.Context) error {
	body, err := h.verifyStatistic(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	filterChecker := make([]interface{}, 0)
	if body.Checker != "" {
		filterChecker = append(filterChecker, map[string]interface{}{
			"term": map[string]interface{}{
				"checker": body.Checker,
			},
		})
	}
	severity := map[string]interface{}{}
	filterV2 := []interface{}{
		map[string]interface{}{
			"term": map[string]interface{}{
				"score.cvss_v2.version": "2.0",
			},
		},
	}
	if len(filterChecker) > 0 {
		filterV2 = append(filterV2, filterChecker...)
	}
	// V2
	queryV2 := map[string]interface{}{
		"bool": map[string]interface{}{
			"filter": filterV2,
		},
	}
	v2, err := h.elastic.Enrichment().CVE().AggregationCount(context.Background(), queryV2, []string{"score.cvss_v2.severity"})
	if err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	sort.Sort(model.Float64Aggregations(v2["score.cvss_v2.severity"]))
	severity["v2"] = v2["score.cvss_v2.severity"]

	// V3
	queryV3 := map[string]interface{}{
		"bool": map[string]interface{}{
			"should": []interface{}{
				map[string]interface{}{
					"term": map[string]interface{}{
						"score.cvss_v3.version": "3.0",
					},
				},
				map[string]interface{}{
					"term": map[string]interface{}{
						"score.cvss_v3.version": "3.1",
					},
				},
			},
		},
	}
	if len(filterChecker) > 0 {
		queryV3["bool"].(map[string]interface{})["filter"] = filterChecker
	}
	v3, err := h.elastic.Enrichment().CVE().AggregationCount(context.Background(), queryV3, []string{"score.cvss_v3.severity"})
	if err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	sort.Sort(model.Float64Aggregations(v3["score.cvss_v3.severity"]))
	severity["v3"] = v3["score.cvss_v3.severity"]
	// V4
	queryV4 := map[string]interface{}{
		"bool": map[string]interface{}{
			"should": []interface{}{
				map[string]interface{}{
					"term": map[string]interface{}{
						"score.cvss_v4.version": "4.0",
					},
				},
			},
		},
	}
	if len(filterChecker) > 0 {
		queryV4["bool"].(map[string]interface{})["filter"] = filterChecker
	}
	v4, err := h.elastic.Enrichment().CVE().AggregationCount(context.Background(), queryV4, []string{"score.cvss_v4.severity"})
	if err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	sort.Sort(model.Float64Aggregations(v4["score.cvss_v4.severity"]))
	severity["v4"] = v4["score.cvss_v4.severity"]
	// Common
	fields := []string{
		"status",
		"checker",
		"score.vti.severity",
		"score.global.version",
		"score.global.severity",
		"languages",
	}
	results := map[string]interface{}{}
	queryAll := defs.ElasticsearchQueryFilterMatchAll
	if len(filterChecker) > 0 {
		queryAll = map[string]interface{}{
			"bool": map[string]interface{}{
				"filter": filterChecker,
			},
		}
	}
	common, err := h.elastic.Enrichment().CVE().AggregationCount(context.Background(), queryAll, fields)
	if err != nil {
		h.logger.Errorf("failed to get common aggregation count. err: %v", err)
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	sort.Sort(model.Float64Aggregations(common["status"]))
	results["status"] = common["status"]
	// Checker
	checkers := make([]es.ResultAggregationCount, 0)
	for _, checker := range common["checker"] {
		if checker.Value != "Unknown" {
			checkers = append(checkers, checker)
		}
	}
	results["checker"] = checkers
	var totalUnknown int64 = 0
	var totalV2 int64 = 0
	var totalV3 int64 = 0
	var totalV4 int64 = 0
	for _, count := range v2["score.cvss_v2.severity"] {
		totalV2 += count.Count
	}
	for _, count := range v3["score.cvss_v3.severity"] {
		totalV3 += count.Count
	}
	for _, count := range v4["score.cvss_v4.severity"] {
		totalV4 += count.Count
	}

	cvss := common["score.global.version"]
	for _, item := range cvss {
		switch item.Value {
		case defs.VersionCvssV20:
			continue
		case defs.VersionCvssV30, defs.VersionCvssV31:
			continue
		case defs.VersionCvssV40:
			continue
		default:
			totalUnknown += item.Count
		}
	}
	results["cvss"] = []es.ResultAggregationCount{
		{
			Value: "4.*",
			Count: totalV4,
		},
		{
			Value: "3.*",
			Count: totalV3,
		},
		{
			Value: "2.*",
			Count: totalV2,
		},
		{
			Value: "N/A",
			Count: totalUnknown,
		},
	}
	sort.Sort(model.Float64Aggregations(common["score.global.severity"]))
	severity["all"] = common["score.global.severity"]
	sort.Sort(model.Float64Aggregations(common["score.vti.severity"]))
	results["severity"] = map[string]interface{}{
		"vti": map[string]interface{}{
			"all": common["score.vti.severity"],
		},
		"global": severity,
	}
	queryLanguages := map[string]interface{}{
		"bool": map[string]interface{}{
			"filter": []interface{}{
				queryAll,
				map[string]interface{}{
					"term": map[string]interface{}{
						"languages": defs.LangEN,
					},
				},
				map[string]interface{}{
					"term": map[string]interface{}{
						"languages": defs.LangVI,
					},
				},
			},
		},
	}
	countLanguages, err := h.elastic.Enrichment().CVE().Count(context.Background(), queryLanguages)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	languages := []es.ResultAggregationCount{
		{
			Value: defs.LangVI,
			Count: 0,
		},
		{
			Value: fmt.Sprintf("%s,%s", defs.LangVI, defs.LangEN),
			Count: countLanguages,
		},
	}
	for _, item := range common["languages"] {
		switch item.Value.(string) {
		case defs.LangVI:
			languages[0].Count = item.Count - countLanguages
		}
	}
	results["languages"] = languages
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(results).Go()
}

func (h *CVEHandler) ExportCveById(c echo.Context) error {
	body, err := h.verifyID(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	bodyPdf := map[string]interface{}{}
	lan := strings.TrimSpace(c.QueryParam("lang"))
	if lan != "vi" && lan != "en" {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log("Bad request lor lang").Go()
	}
	body.ID = strings.TrimSpace(body.ID)
	document, err := h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() == es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusNotFound).Go()
		}
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	response := &model.CVEVerbose{
		CVE:      *document,
		Products: make([]*model.CPEDetail, 0),
		Clients:  make([]*model.GroupUser, 0),
		CVSS:     map[string]interface{}{},
	}

	languages, err := h.elastic.Enrichment().CVELang("*").FindByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
		languages = make([]*model.CVELang, 0)
	}
	description := ""
	for _, language := range languages {
		description = language.Description
		language.Description = language.Raw
		if lan == language.Lang {
			bodyPdf["summary"] = language.Raw
			//if len(language.Raw) != 0 {
			// bodyPdf["summary"] = language.Raw
			//} else {
			// bodyPdf["summary"] = "N/A"
			//}
			bodyPdf["reference"] = language.Reference
			//if len(language.Reference) != 0 {
			// bodyPdf["reference"] = language.Reference
			//} else {
			// bodyPdf["reference"] = "N/A"
			//}

			bodyPdf["solution"] = language.Patch
			//if len(language.Patch) != 0 {
			// bodyPdf["solution"] = language.Patch
			//} else {
			// bodyPdf["solution"] = "N/A"
			//}
		}
	}

	if bodyPdf["solution"] == nil {
		bodyPdf["solution"] = []string{}
	}
	if bodyPdf["reference"] == nil {
		bodyPdf["reference"] = []string{}
	}
	if bodyPdf["summary"] == nil {
		bodyPdf["summary"] = ""
	}

	bodyPdf["lang"] = lan
	bodyPdf["cve_name"] = response.CVE.Name
	bodyPdf["cvss_score"] = response.CVE.Score.Global.Score
	bodyPdf["cvss_severity"] = response.CVE.Score.Global.Severity
	bodyPdf["vcs_severity"] = response.CVE.Score.VTI.Severity
	bodyPdf["cve_owner"] = response.CVE.Vendor
	if response.CVE.Status == defs.AssetStatusCodeApproved {
		bodyPdf["cve_date"] = int(response.CVE.Approved)
	} else {
		bodyPdf["cve_date"] = int(response.CVE.Modified)
	}

	// Match
	cpeRaw := make([]*model.CPERaw, 0)
	if len(document.Match) > 0 {
		cpeRaw, err = h.GetCPE(document.Match)
		if err != nil {
			if err.Error() != es.NotFoundError {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
			h.logger.Errorf("failed to get CPE, reason: %v", err)
		}
	}

	listProduct := []string{}
	productMap := make(map[string]struct{})
	cpes := make([]*model.CPERaw, 0)
	for _, cpe := range cpeRaw {
		if cpe != nil {
			productStr := strings.ReplaceAll(cpe.Product, "-", "_")
			productPath := strings.Split(productStr, "_")
			productVerbose := make([]string, 0)
			for _, v := range productPath {
				productVerbose = append(productVerbose, strings.Title(strings.ToLower(v)))
			}
			cpe.Product = strings.Join(productVerbose, " ")
			cpes = append(cpes, cpe)

			if _, ok := productMap[cpe.Product]; !ok {
				productMap[cpe.Product] = struct{}{}
				listProduct = append(listProduct, cpe.Product)
			}
		}
	}

	if len(listProduct) > 3 {
		if len(listProduct) == 0 {
			bodyPdf["cve_product"] = "N/A"
		} else {
			bodyPdf["cve_product"] = strings.Join(listProduct[0:3], ", ") + ", ..."
		}
	} else {
		if len(listProduct) == 0 {
			bodyPdf["cve_product"] = "N/A"
		} else {
			bodyPdf["cve_product"] = strings.Join(listProduct, ", ")
		}
	}
	bodyPdf["products"] = cpes
	// CVSS
	raw, err := h.elastic.Enrichment().CVERaw().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	} else {
		switch document.Score.Global.Version {
		case defs.VersionCvssV20:
			response.CVSS = raw.Impact.MetricV2.Vector.Result()
		case defs.VersionCvssV30, defs.VersionCvssV31:
			response.CVSS = raw.Impact.MetricV3.Vector.Result()
		}
	}

	if len(response.CVSS) != 0 {
		bodyPdf["attack_complexity"] = response.CVSS["Attack Complexity"]
		bodyPdf["attack_vector"] = response.CVSS["Attack Vector"]
		bodyPdf["availability_impact"] = response.CVSS["Availability Impact"]
		bodyPdf["base_severity"] = response.CVSS["Base Severity"]
		bodyPdf["confidentiality_impact"] = response.CVSS["Confidentiality Impact"]
		bodyPdf["integrity_impact"] = response.CVSS["Integrity Impact"]
		bodyPdf["privileges_required"] = response.CVSS["Privileges Required"]
		bodyPdf["scope"] = response.CVSS["Scope"]
		bodyPdf["user_interaction"] = response.CVSS["User Interaction"]
	} else {
		bodyPdf["attack_complexity"] = "N/A"
		bodyPdf["attack_vector"] = "N/A"
		bodyPdf["availability_impact"] = "N/A"
		bodyPdf["base_severity"] = "N/A"
		bodyPdf["confidentiality_impact"] = "N/A"
		bodyPdf["integrity_impact"] = "N/A"
		bodyPdf["privileges_required"] = "N/A"
		bodyPdf["scope"] = "N/A"
		bodyPdf["user_interaction"] = "N/A"
	}
	// Call
	client := resty.New()
	client.SetTimeout(time.Minute * 2)
	client.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	res, err := client.R().SetHeader(rest.HeaderContentType, rest.MIMEApplicationJSON).SetBody(bodyPdf).Post(h.config.Api.Export)
	if err != nil || res.StatusCode() != rest.StatusOK {
		h.logger.Debug("Recall api export")
		bodyPdf["summary"] = description
		res, err = client.R().SetHeader(rest.HeaderContentType, rest.MIMEApplicationJSON).SetBody(bodyPdf).Post(h.config.Api.Export)
		if err != nil || res.StatusCode() != rest.StatusOK {
			return c.JSON(http.StatusInternalServerError, err.Error())
		}
	}

	cvename := strings.Split(response.CVE.Name, "-")
	fileName := fmt.Sprintf("CVE_%v_%v.pdf", lan, strings.Join(cvename[1:], "_"))

	c.Response().Header().Set("Content-Type", "application/pdf")
	c.Response().Header().Set("Content-Disposition", "attachment; filename="+fileName)
	c.Response().Header().Set("Content-Transfer-Encoding", "binary")
	if _, err = c.Response().Write(res.Body()); err != nil {
		return nil
	}
	return nil
}

func (h *CVEHandler) GetCPE(match []string) ([]*model.CPERaw, error) {
	result := make([]*model.CPERaw, 0)
	for _, pro := range match {
		saved, err := h.elastic.Enrichment().CPE().FindByValue(context.Background(), pro)
		if err != nil {
			if err.Error() != es.NotFoundError {
				return result, err
			}
			newCPE := model.NewCPE(pro)
			if newCPE != nil {
				saved = &model.CPE{
					CPEDetail: newCPE.CPEDetail,
				}
			}
		}
		if saved != nil {
			result = append(result, &model.CPERaw{
				Part:       saved.CPEDetail.Part,
				References: saved.Reference,
				SwEdition:  saved.SwEdition,
				Version:    saved.CPEDetail.Version,
				ID:         saved.CPEDetail.ID,
				Created:    saved.CPEDetail.Created,
				Value:      saved.CPEDetail.Value,
				Name:       saved.Name,
				Edition:    saved.Edition,
				Other:      saved.Other,
				Creator:    saved.CPEDetail.Creator,
				Vendor:     saved.CPEDetail.Vendor,
				Language:   saved.Language,
				Product:    saved.CPEDetail.Product,
				Update:     saved.CPEDetail.Update,
				TargetSw:   saved.TargetSw,
				TargetHw:   saved.TargetHw,
			})
		}

	}
	return result, nil
}

func (h *CVEHandler) Detail(c echo.Context) error {
	body, err := h.verifyID(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	document, err := h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() == es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusNotFound).Go()
		}
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	if document.Status != defs.StatusCodeApproved {
		document.Approved = 0
	}
	response := &model.CVEVerbose{
		CVE:      *document,
		Products: make([]*model.CPEDetail, 0),
		Clients:  make([]*model.GroupUser, 0),
		CVSS:     map[string]interface{}{},
	}
	// Match
	if len(document.Match) > 0 {
		missing := make([]*model.CPEMeta, 0)
		for _, pro := range document.Match {
			saved, err := h.elastic.Enrichment().CPE().FindByValue(context.Background(), pro)
			if err != nil {
				if err.Error() != es.NotFoundError {
					return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
				}
				h.logger.Errorf("failed to get CPE (%s), reason: %v", pro, err)
				newCPE := model.NewCPE(pro)
				if newCPE != nil {
					missing = append(missing, newCPE)
					saved = &model.CPE{
						CPEDetail: newCPE.CPEDetail,
					}
				}
			}
			if saved != nil {
				response.Products = append(response.Products, &saved.CPEDetail)
			}
		}
		if len(missing) > 0 {
			go h.addProduct(missing)
		}
		sort.Sort(model.CPEDetails(response.Products))
	}
	// Group User
	if len(document.Customer) > 0 {
		for _, group := range document.Customer {
			customer := &model.GroupUser{
				TenantID: group,
				Name:     group,
			}
			saved, err := h.mongo.Account().GroupUser().FindByTenantID(group)
			if err != nil {
				h.logger.Errorf("failed to get Group User (%s), reason: %v", group, err)
			} else {
				customer.Name = saved.Name
			}
			response.Clients = append(response.Clients, customer)
		}
		sort.Sort(model.GroupUsers(response.Clients))
	}
	// CVSS
	raw, err := h.elastic.Enrichment().CVERaw().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	} else {
		switch document.Score.Global.Version {
		case defs.VersionCvssV20:
			response.CVSS = raw.Impact.MetricV2.Vector.Result()
		case defs.VersionCvssV30, defs.VersionCvssV31:
			response.CVSS = raw.Impact.MetricV3.Vector.Result()
		case defs.VersionCvssV40:
			response.CVSS = raw.Impact.MetricV4.Vector.Result()
		}
	}
	languages, err := h.elastic.Enrichment().CVELang("*").FindByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
		languages = make([]*model.CVELang, 0)
	}
	for _, language := range languages {
		language.Description = language.Raw
		switch language.Lang {
		case defs.LangVI:
			response.MultiLang.VI = language
		case defs.LangEN:
			response.MultiLang.EN = language
		}
	}
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(response).Go()
}

func (h *CVEHandler) Create(c echo.Context) error {
	body, err := h.verifyCreate(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	editor := c.Get("user_name").(string)
	document, language := body.Generate()
	_, err = h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	} else {
		return rest.JSON(c).Code(rest.StatusConflict).Go()
	}
	if len(body.Match) == 0 {
		query := body.Product.PrepareQuery()
		products, err := h.elastic.Enrichment().CPE().FindAll(context.Background(), query, []string{})
		if err != nil {
			if err.Error() == es.NotFoundError {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
			products = make([]*model.CPE, 0)
		}
		for _, pro := range products {
			document.Match = append(document.Match, pro.Value)
		}
	}
	document.Vendor = document.GetVendor()
	// Store history
	newDataJSON, _ := json.Marshal(document)
	history := &model.History{
		Created:  document.Created,
		Document: document.ID,
		Editor:   editor,
		Action:   defs.ActionCreateCVE,
		OldData:  "",
		NewData:  string(newDataJSON),
	}
	history.GenID()
	if err = h.elastic.Enrichment().CVEHistory().Store(context.Background(), history); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	// Store cve epss history
	if document.EPSS.Score > 0 || document.EPSS.Percentile > 0 {
		epssHistory := &model.CVEEPSSHistory{
			CVEName:       document.ID,
			Date:          time.Unix(document.Created, 0).Format("2006-01-02"),
			Editor:        editor,
			OldScore:      0,
			NewScore:      document.EPSS.Score,
			OldPercentile: 0,
			NewPercentile: document.EPSS.Percentile,
		}
		epssHistory.GenID()

		if err = h.elastic.Enrichment().CVEEPSSHistory().Store(context.Background(), epssHistory); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}

	// Match
	if err = h.match(document); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	if document.Affect > 0 {
		isOnlyMass := true
		for _, org := range document.Customer {
			group, err := h.GetGroup(org)
			if err != nil {
				if org != "banking" {
					isOnlyMass = false
					break
				}

				continue
			}
			isExistedPerm := false
			for _, it := range group.Permissions {
				if it == defs.PermissionAutoAlert {
					isExistedPerm = true
					break
				}
			}
			if !isExistedPerm {
				isOnlyMass = false
				break
			}
		}

		if len(document.Customer) == 1 && document.Customer[0] == "banking" {
			isOnlyMass = false
		}

		if isOnlyMass && document.Status == defs.StatusCodeNew {
			document.Status = defs.AssetStatusCodeApproved
			document.Approved = document.Created
		}
	}

	// Store cve
	if err = h.elastic.Enrichment().CVE().Store(context.Background(), document); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Store language
	if err = h.elastic.Enrichment().CVELang(language.Lang).Store(context.Background(), language); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Publish cve
	//if err = h.publish(&model.CVEDelivery{
	// CVE: *document,
	// Delivery: false,
	//}); err != nil {
	// return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	//}
	if err = h.createLifecycle(document, &model.CVELifecycle{Source: model.CVE_SOURCE_VTI, Event: model.CVE_EVENT_CREATE_CVE, Creator: editor}); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Cache cve
	if len(document.Match) > 0 {
		if err = h.cache.Strings().Set(document.Name, strings.Join(document.Match, ";"), 0); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}
	if document.AnalysisTime > 0 {
		if err = h.createLifecycle(document, &model.CVELifecycle{Source: model.CVE_SOURCE_VTI, Event: model.CVE_EVENT_ANALYSIS_TIME, Creator: editor}); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(document.ID).Go()
}

func (h *CVEHandler) Edit(c echo.Context) error {
	body, err := h.verifyEdit(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}

	editor := c.Get("user_name").(string)

	saved, err := h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() == es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusNotFound).Go()
		}
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	oldEPSSScore := saved.EPSS.Score
	oldEPSSPercentile := saved.EPSS.Percentile

	oldDataJSON, _ := json.Marshal(saved)

	now, _ := clock.Now(clock.Local)
	saved.Modified = clock.UnixMilli(now)
	saved.Published = body.Published

	if len(body.Match) == 0 {
		query := body.Product.PrepareQuery()
		products, err := h.elastic.Enrichment().CPE().FindAll(context.Background(), query, []string{})
		if err != nil {
			if err.Error() == es.NotFoundError {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
			products = make([]*model.CPE, 0)
		}
		for _, pro := range products {
			saved.Match = append(saved.Match, pro.Value)
		}
	} else {
		saved.Match = body.Match
	}

	if len(body.CVSS) > 0 {
		saved.Score.Scores = body.CVSS

		versionPriority := utils.GetCVSSVersionPriority()
		var highestVersion model.CVEMetric
		highestVersionNum := 0.0

		saved.Score.CVSS2 = model.CVEMetric{}
		saved.Score.CVSS3 = model.CVEMetric{}
		saved.Score.CVSS4 = model.CVEMetric{}

		for _, cvss := range body.CVSS {
			switch cvss.Version {
			case defs.VersionCvssV20:
				saved.Score.CVSS2 = cvss
			case defs.VersionCvssV31:
				saved.Score.CVSS3 = cvss
			case defs.VersionCvssV40:
				saved.Score.CVSS4 = cvss
			}

			if versionNum := versionPriority[cvss.Version]; versionNum > highestVersionNum {
				highestVersionNum = versionNum
				highestVersion = cvss
			}
		}

		saved.Score.Global = highestVersion
	}

	saved.EPSS = body.EPSS

	saved.CWE = body.CWE

	saved.Vendor = saved.GetVendor()

	if saved.Status == defs.StatusCodeReject {
		saved.Status = defs.StatusCodeNew
	}

	var language *model.CVELang
	language, err = h.elastic.Enrichment().CVELang(body.Lang).GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
		language = &model.CVELang{
			ID:        saved.ID,
			Lang:      body.Lang,
			Reference: []string{},
			Patch:     []string{},
		}
	}

	description := strings.TrimSpace(html2text.HTML2Text(body.Description))
	language.Description = description
	language.Raw = body.Description
	language.Reference = strings.Split(body.Reference, "\n")
	language.Patch = strings.Split(body.Patch, "\n")

	switch body.Lang {
	case defs.LangVI:
		saved.Searchable.VI.Description = description
		saved.Searchable.VI.Reference = language.Reference
	case defs.LangEN:
		saved.Searchable.EN.Description = description
		saved.Searchable.EN.Reference = language.Reference
	}

	saved.Languages = slice.String(append(saved.Languages, body.Lang)).Unique().Extract()
	newDataJSON, _ := json.Marshal(saved)
	history := &model.History{
		Created:  saved.Modified,
		Document: saved.ID,
		Editor:   editor,
		Action:   defs.ActionEditCVE,
		OldData:  string(oldDataJSON),
		NewData:  string(newDataJSON),
	}
	history.GenID()
	if err = h.elastic.Enrichment().CVEHistory().Store(context.Background(), history); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	newEPSSScore := saved.EPSS.Score
	newEPSSPercentile := saved.EPSS.Percentile
	if oldEPSSScore != newEPSSScore || oldEPSSPercentile != newEPSSPercentile {
		epssHistory := &model.CVEEPSSHistory{
			CVEName:       saved.ID,
			Date:          time.Unix(saved.Modified/1000, 0).Format("2006-01-02"),
			Editor:        editor,
			OldScore:      oldEPSSScore,
			NewScore:      newEPSSScore,
			OldPercentile: oldEPSSPercentile,
			NewPercentile: newEPSSPercentile,
		}
		epssHistory.GenID()

		if err = h.elastic.Enrichment().CVEEPSSHistory().Store(context.Background(), epssHistory); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}

	oldAnalysisTime := saved.AnalysisTime

	if err = h.match(saved); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	isApprovedCve := false
	if saved.Affect > 0 {
		isOnlyMass := true
		for _, org := range saved.Customer {
			group, err := h.GetGroup(org)
			if err != nil {
				if org != "banking" {
					isOnlyMass = false
					break
				}
				continue
			}
			isExistedPerm := false
			for _, it := range group.Permissions {
				if it == defs.PermissionAutoAlert {
					isExistedPerm = true
					break
				}
			}
			if !isExistedPerm {
				isOnlyMass = false
				break
			}
		}

		if len(saved.Customer) == 1 && saved.Customer[0] == "banking" {
			isOnlyMass = false
		}

		if isOnlyMass && saved.Status == defs.StatusCodeNew {
			saved.Status = defs.AssetStatusCodeApproved
			saved.Approved = saved.Modified
			isApprovedCve = true
		}
	}

	if err = h.elastic.Enrichment().CVE().Store(context.Background(), saved); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	if err = h.elastic.Enrichment().CVELang(body.Lang).Store(context.Background(), language); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	if isApprovedCve {
		if err = h.createLifecycle(saved, &model.CVELifecycle{
			Source:  model.CVE_SOURCE_VTI,
			Event:   model.CVE_EVENT_APPROVE_CVE,
			Creator: editor,
		}); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}

	if oldAnalysisTime == 0 && saved.AnalysisTime > 0 {
		if err = h.createLifecycle(saved, &model.CVELifecycle{
			Source:  model.CVE_SOURCE_VTI,
			Event:   model.CVE_EVENT_ANALYSIS_TIME,
			Creator: editor,
		}); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}

	if len(saved.Match) > 0 {
		if err = h.cache.Strings().Set(saved.Name, strings.Join(saved.Match, ";"), 0); err != nil {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}

	return rest.JSON(c).Code(rest.StatusOK).Body(saved.ID).Go()
}

func (h *CVEHandler) Exist(c echo.Context) error {
	body, err := h.verifyID(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	_, err = h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() == es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusOK).Go()
		}
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	} else {
		return rest.JSON(c).Code(rest.StatusConflict).Go()
	}
}

func (h *CVEHandler) History(c echo.Context) error {
	body, err := h.verifyHistory(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	query := map[string]interface{}{
		"bool": map[string]interface{}{
			"filter": []interface{}{
				map[string]interface{}{
					"term": map[string]interface{}{
						"document": body.ID,
					},
				},
			},
		},
	}
	results, err := h.elastic.Enrichment().CVEHistory().Find(context.Background(), query, []string{"-created"}, body.Offset, body.Size)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
		return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": make([]interface{}, 0), "total": 0}).Go()
	}
	count, err := h.elastic.Enrichment().CVEHistory().Count(context.Background(), query)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": make([]interface{}, 0), "total": 0}).Go()
	}
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"data": results, "total": count}).Go()
}

func (h *CVEHandler) Confirm(c echo.Context) error {
	body, err := h.verifyConfirm(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	editor := c.Get("user_name").(string)
	saved, err := h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		if err.Error() == es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusNotFound).Log(err).Go()
		}
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	if body.Checklist.Metric.EQ(saved.Checklist.Metric) && saved.Status == body.Status {
		return rest.JSON(c).Code(rest.StatusOK).Go()
	}
	now, _ := clock.Now(clock.Local)
	saved.Modified = clock.UnixMilli(now)
	saved.Checklist = body.Checklist
	saved.Score.VTI.Score = float32(body.Checklist.Point)
	saved.Score.VTI.SetVTIMetric()
	saved.Status = body.Status
	saved.Checker = editor
	// Store history
	history := &model.History{
		Created:     saved.Modified,
		Document:    saved.ID,
		Editor:      editor,
		Description: body.Description,
	}
	history.GenID()
	switch saved.Status {
	case defs.StatusCodeApproved:
		history.Action = defs.ActionApproveCVE
		saved.Approved = clock.UnixMilli(now)
	case defs.StatusCodeReject:
		history.Action = defs.ActionRejectCVE
	}
	if err = h.elastic.Enrichment().CVEHistory().Store(context.Background(), history); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Match
	if err = h.match(saved); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Store cve
	if err = h.elastic.Enrichment().CVE().Store(context.Background(), saved); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Publish cve
	if err = h.publish(&model.CVEDelivery{
		CVE:      *saved,
		Delivery: body.Status == defs.StatusCodeApproved,
	}); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	// Create lifecycle
	if err = h.createLifecycle(saved, &model.CVELifecycle{Source: model.CVE_SOURCE_VTI, Event: model.CVE_EVENT_APPROVE_CVE, Creator: editor}); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Go()
}

func (h *CVEHandler) createLifecycle(document *model.CVE, req *model.CVELifecycle) error {
	now, _ := clock.Now(clock.Local)
	created := clock.UnixMilli(now)
	req.Created = created
	req.CVEId = document.ID
	req.CVECode = document.Name
	req.GenerateId(document.ID)

	return h.elastic.Enrichment().CVELifecycle().Store(context.Background(), req)
}

func (h *CVEHandler) CreateLifecycle(c echo.Context) error {
	body, err := h.verifyCreateLifecycle(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}
	doc, err := h.elastic.Enrichment().CVE().GetByID(context.Background(), body.ID)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusNotFound).Go()
	}
	if err = h.createLifecycle(doc, &model.CVELifecycle{Source: body.Source, Event: body.Event}); err != nil {
		return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
	}

	return rest.JSON(c).Code(rest.StatusOK).Go()
}

func (h *CVEHandler) CheckReject(c echo.Context) error {
	body, err := h.verifySearch(c)
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}

	if body.Time.Approved.Gte > 0 || body.Time.Approved.Lte > 0 {
		if len(body.Status) > 1 || (len(body.Status) == 1 && body.Status[0] != 2) {
			return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"message": "True"}).Go()
		}
	}

	query := body.PrepareQuery()
	results, err := h.elastic.Enrichment().CVE().Find(context.Background(), query, body.Sort, body.Offset, body.Size)
	if err != nil {
		if err.Error() != es.NotFoundError {
			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	}
	for _, cve := range results {
		if cve.Status != defs.StatusCodeNew {
			err = errors.New(fmt.Sprintf("invalid status cve %s", cve.Name))
			return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"message": "False"}).Go()
		}
	}
	return rest.JSON(c).Code(rest.StatusOK).Body(map[string]interface{}{"message": "True"}).Go()
}

func (h *CVEHandler) RejectCVEs(c echo.Context) error {
	body, err := h.verifyRejectCVEs(c)
	body.Status = defs.StatusCodeReject
	if err != nil {
		return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
	}

	if len(body.IDs) == 0 {
		err = h.verifySearchReject(body.Filter)
		if err != nil {
			return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
		}
	}

	editor := c.Get("user_name").(string)
	should := make([]interface{}, 0)
	for _, id := range body.IDs {
		should = append(should, map[string]interface{}{
			"term": map[string]interface{}{
				"id": id,
			},
		})
	}
	query := map[string]interface{}{
		"bool": map[string]interface{}{
			"should": should,
		},
	}
	var listCVE []*model.CVE
	if len(body.IDs) > 0 {
		listCVE, err = h.elastic.Enrichment().CVE().Find(context.Background(), query, []string{}, 0, len(body.IDs))
		if err != nil {
			if err.Error() == es.NotFoundError {
				return rest.JSON(c).Code(rest.StatusNotFound).Log(err).Go()
			}

			return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
		}
	} else {
		if body.Filter.Time.Approved.Gte > 0 || body.Filter.Time.Approved.Lte > 0 {
			if len(body.Filter.Status) > 1 || (len(body.Filter.Status) == 1 && body.Filter.Status[0] != 2) {
				return rest.JSON(c).Code(rest.StatusOK).Body(body.IDs).Go()
			}
		}
		query := body.Filter.PrepareQuery()
		listCVE, err = h.elastic.Enrichment().CVE().Find(context.Background(), query, body.Filter.Sort, body.Filter.Offset, body.Filter.Size)
		if err != nil {
			if err.Error() != es.NotFoundError {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
		}
	}

	for _, cve := range listCVE {
		if cve.Status != defs.StatusCodeNew {
			err = errors.New(fmt.Sprintf("invalid status cve %s", cve.Name))
			return rest.JSON(c).Code(rest.StatusBadRequest).Log(err).Go()
		}
	}

	group, _ := errgroup.WithContext(context.Background())
	type results struct {
		err   error
		value *model.CVE
	}
	core := h.config.App.Worker
	if core == 0 {
		core = defs.WorkerDefault
	}
	result := make(chan results)
	group.Go(func() error {
		wg := sync.WaitGroup{}
		pool, _ := ants.NewPoolWithFunc(core, func(i interface{}) {
			saved := i.(*model.CVE)
			defer wg.Done()
			defer func() {
				rs := results{
					err:   err,
					value: saved,
				}
				result <- rs
			}()
			if body.Checklist.Metric.EQ(saved.Checklist.Metric) && saved.Status == body.Status {
				return
			}
			now, _ := clock.Now(clock.Local)
			saved.Modified = clock.UnixMilli(now)
			saved.Checklist = body.Checklist
			saved.Score.VTI.Score = float32(body.Checklist.Point)
			saved.Score.VTI.SetVTIMetric()
			saved.Status = body.Status
			saved.Checker = editor
			// Store history
			history := &model.History{
				Created:     saved.Modified,
				Document:    saved.ID,
				Editor:      editor,
				Description: body.Description,
			}
			history.GenID()
			history.Action = defs.ActionRejectCVE

			if err = h.elastic.Enrichment().CVEHistory().Store(context.Background(), history); err != nil {
				return
			}
			// Match
			if err = h.match(saved); err != nil {
				return
			}
			// Store cve
			if err = h.elastic.Enrichment().CVE().Store(context.Background(), saved); err != nil {
				return
			}
			// Publish cve
			if err = h.publish(&model.CVEDelivery{
				CVE:      *saved,
				Delivery: body.Status == defs.StatusCodeApproved,
			}); err != nil {
				return
			}
		})
		defer pool.Release()

		for _, cve := range listCVE {
			wg.Add(1)
			if err := pool.Invoke(cve); err != nil {
				return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
			}
		}
		wg.Wait()
		close(result)
		return nil
	})
	group.Go(func() error {
		for value := range result {
			if value.err != nil {
				if err.Error() != es.NotFoundError {
					return rest.JSON(c).Code(rest.StatusInternalServerError).Log(err).Go()
				}
			}
		}
		return nil
	})
	// Success
	return rest.JSON(c).Code(rest.StatusOK).Body(body.IDs).Go()
}

func (h *CVEHandler) publish(document *model.CVEDelivery) error {
	bts, err := json.Marshal(document)
	if err != nil {
		return err
	}
	if err := h.queue.Publish("", defs.QueueCVEAnalyzer, rabbit.Message{
		Body:        bts,
		ContentType: rabbit.MIMEApplicationJSON,
		Mode:        rabbit.Persistent,
	}); err != nil {
		return err
	}
	// Success
	return nil
}

func (h *CVEHandler) match(document *model.CVE) error {
	// Analyzer cve
	h.mutex.Lock()
	match := make([]string, 0)
	for _, m := range document.Match {
		actual, err := cpe.NewItemFromFormattedString(m)
		if err != nil {
			h.logger.Errorf("failed to parse cpe (%s), reason: %v", m, err)
			continue
		}
		for org, assets := range h.assets {
			for _, asset := range assets {
				if cpe.CheckSuperset(asset, actual, true) || cpe.CheckSubset(asset, actual, true) {
					if !slice.String(match).Contains(org) {
						match = append(match, org)
					}
					break
				}
			}
		}
	}
	document.Customer = match
	document.Affect = int64(len(match))
	if document.Status == defs.StatusCodeUnknown || document.Status == defs.StatusCodeNew {
		if document.Affect > 0 {
			document.Status = defs.StatusCodeNew
		} else {
			document.Status = defs.StatusCodeUnknown
		}
	}
	if document.AnalysisTime == 0 &&
		(document.Score.Global.Version == defs.VersionCvssV30 ||
			document.Score.Global.Version == defs.VersionCvssV31 ||
			document.Score.Global.Version == defs.VersionCvssV40) &&
		document.Affect > 0 && (document.Score.Global.Score == 0 || document.Score.Global.Score >= 7) {
		document.AnalysisTime = document.Modified
	}
	h.mutex.Unlock()
	// Success
	return nil
}

func (h *CVEHandler) verifySearch(c echo.Context) (body model.RequestCVESearch, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	err = body.Verify()
	if err != nil {
		return body, err
	}
	// Success
	return body, nil
}

func (h *CVEHandler) verifyExport(c echo.Context) (body model.RequestExport, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	err = body.Req.Verify()
	if err != nil {
		return body, err
	}
	// Success
	return body, nil
}

func (h *CVEHandler) verifyStatistic(c echo.Context) (body model.RequestCVEStatistic, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	if body.Checker != "" {
		body.Checker = strings.ToLower(body.Checker)
	}
	// Success
	return body, nil
}

func (h *CVEHandler) verifyID(c echo.Context) (body model.RequestCVEID, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	body.ID = strings.ToUpper(body.ID)
	re := regexp.MustCompile(defs.RegexCVE)
	if re.MatchString(body.ID) {
		body.ID = hash.SHA1(strings.ToUpper(body.ID))
	} else {
		body.ID = strings.ToLower(body.ID)
	}
	// Success
	return body, nil
}

func (h *CVEHandler) verifyCreate(c echo.Context) (body model.RequestCVECreate, err error) {
	// Debug: Log request info
	log.Printf("=== DEBUG START ===")
	log.Printf("Request Method: %s", c.Request().Method)
	log.Printf("Request URL: %s", c.Request().URL.String())
	log.Printf("Content-Type: %s", c.Request().Header.Get("Content-Type"))
	log.Printf("Content-Length: %d", c.Request().ContentLength)

	// Debug: Log all headers
	for name, values := range c.Request().Header {
		for _, value := range values {
			log.Printf("Header %s: %s", name, value)
		}
	}

	// Debug: Read raw request body
	bodyBytes, err := ioutil.ReadAll(c.Request().Body)
	if err != nil {
		log.Printf("Error reading body: %v", err)
		return body, err
	}
	log.Printf("Raw request body length: %d", len(bodyBytes))
	log.Printf("Raw request body: %s", string(bodyBytes))

	// Reset body để Bind có thể đọc
	c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes))
	log.Printf("Body reset completed")

	// Test bind directly
	log.Printf("About to call Validate...")
	if err = Validate(c, &body); err != nil {
		log.Printf("Validate error: %v", err)
		return body, err
	}
	log.Printf("Validate completed successfully")

	// Debug: Log body after bind
	log.Printf("Body after bind: %+v", body)
	log.Printf("CVSS data: %+v", body.CVSS)
	log.Printf("CVSS length: %d", len(body.CVSS))
	log.Printf("=== DEBUG END ===")

	body.ID, err = validateCVEID(body.ID)
	if err != nil {
		return body, err
	}

	if len(body.Match) == 0 {
		body.Match = make([]string, 0)
	}

	body.CVSS, err = validateCVSS(body.CVSS, true)
	if err != nil {
		return body, err
	}

	if err = validateEPSS(body.EPSS); err != nil {
		return body, err
	}

	body.CWE, err = validateCWE(body.CWE)
	if err != nil {
		return body, err
	}

	body.Lang, err = validateLanguage(body.Lang, false, defs.LangVI)
	if err != nil {
		return body, err
	}

	return body, nil
}

func (h *CVEHandler) verifyEdit(c echo.Context) (body model.RequestCVEEdit, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}

	body.ID, err = validateCVEID(body.ID)
	if err != nil {
		return body, err
	}
	body.ID = hash.SHA1(body.ID)

	if len(body.Match) == 0 {
		body.Match = make([]string, 0)
	}

	body.CVSS, err = validateCVSS(body.CVSS, false)
	if err != nil {
		return body, err
	}

	if err = validateEPSS(body.EPSS); err != nil {
		return body, err
	}

	body.CWE, err = validateCWE(body.CWE)
	if err != nil {
		return body, err
	}

	body.Lang, err = validateLanguage(body.Lang, true, "")
	if err != nil {
		return body, err
	}

	return body, nil
}

func (h *CVEHandler) verifyHistory(c echo.Context) (body model.RequestHistorySearch, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	body.ID = strings.ToLower(body.ID)
	if strings.Contains(body.ID, "cve-") {
		body.ID = hash.SHA1(strings.ToUpper(body.ID))
	}
	if body.Size == 0 {
		body.Size = 10
	}
	// Success
	return body, nil
}

func (h *CVEHandler) verifyConfirm(c echo.Context) (body model.RequestCVEConfirm, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	body.ID = strings.ToLower(body.ID)
	if strings.Contains(body.ID, "cve-") {
		body.ID = hash.SHA1(strings.ToUpper(body.ID))
	}
	if body.Status != defs.StatusCodeApproved && body.Status != defs.StatusCodeReject {
		return body, errors.New("invalid value for parameter <status>")
	}
	if body.Checklist.Metric.Ability == defs.PointNil {
		return body, errors.New("invalid value for parameter <checklist.metric.ability>")
	}
	if body.Checklist.Metric.Affect == defs.PointNil {
		return body, errors.New("invalid value for parameter <checklist.metric.affect>")
	}
	if body.Checklist.Metric.Condition == defs.PointNil {
		return body, errors.New("invalid value for parameter <checklist.metric.condition>")
	}
	if body.Checklist.Metric.Patch == defs.PointNil {
		return body, errors.New("invalid value for parameter <checklist.metric.patch>")
	}
	if body.Checklist.Metric.Exploit == defs.PointNil {
		return body, errors.New("invalid value for parameter <checklist.metric.exploit>")
	}

	if body.Checklist.Metric.Calculate() != body.Checklist.Point {
		return body, errors.New("wrong checklist point")
	}
	// Success
	return body, nil
}

func (h *CVEHandler) verifyCreateLifecycle(c echo.Context) (body model.CreateLifecycleRequest, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	body.ID = strings.ToUpper(body.ID)
	re := regexp.MustCompile(defs.RegexCVE)
	if re.MatchString(body.ID) {
		body.ID = hash.SHA1(strings.ToUpper(body.ID))
	}

	return body, err
}

func (h *CVEHandler) verifyRejectCVEs(c echo.Context) (body model.RequestCVEsReject, err error) {
	if err = Validate(c, &body); err != nil {
		return body, err
	}
	ids := make([]string, 0)
	for _, id := range body.IDs {
		id = strings.ToLower(id)
		if strings.Contains(id, "cve-") {
			ids = append(ids, hash.SHA1(strings.ToUpper(id)))
		}
	}
	body.IDs = ids
	mapChecklist := mapChecklist{}
	jsonStr, err := json.Marshal(defs.MappingCveChecklistEN[0])
	if err != nil {
		return body, errors.New(fmt.Sprintf("marhsal mapping cve checklist error: %v", err))
	}
	err = json.Unmarshal(jsonStr, &mapChecklist)
	if err != nil {
		return body, errors.New(fmt.Sprintf("unmarhsal mapping cve checklist error: %v", err))
	}

	invalid := true
	for _, point := range mapChecklist.Ability.Checklist {
		if body.Checklist.Metric.Ability == point.Point {
			invalid = false
			break
		}
	}
	if invalid {
		return body, errors.New("invalid value for parameter <checklist.metric.ability>")
	}

	invalid = true
	for _, point := range mapChecklist.Affect.Checklist {
		if body.Checklist.Metric.Affect == point.Point {
			invalid = false
			break
		}
	}
	if invalid {
		return body, errors.New("invalid value for parameter <checklist.metric.affect>")
	}

	invalid = true
	for _, point := range mapChecklist.Condition.Checklist {
		if body.Checklist.Metric.Condition == point.Point {
			invalid = false
			break
		}
	}
	if invalid {
		return body, errors.New("invalid value for parameter <checklist.metric.condition>")
	}

	invalid = true
	for _, point := range mapChecklist.Patch.Checklist {
		if body.Checklist.Metric.Patch == point.Point {
			invalid = false
			break
		}
	}
	if invalid {
		return body, errors.New("invalid value for parameter <checklist.metric.patch>")
	}

	invalid = true
	for _, point := range mapChecklist.Exploit.Checklist {
		if body.Checklist.Metric.Exploit == point.Point {
			invalid = false
			break
		}
	}
	if invalid {
		return body, errors.New("invalid value for parameter <checklist.metric.exploit>")
	}

	if body.Checklist.Metric.Calculate() != body.Checklist.Point {
		return body, errors.New("wrong checklist point")
	}
	if len(body.IDs) > 0 {
		return body, nil
	}

	err = body.Filter.Verify()
	if err != nil {
		return body, err
	}
	// Success
	return body, nil
}

func (h *CVEHandler) addProduct(products []*model.CPEMeta) {
	for _, c := range products {
		bts, err := json.Marshal(c)
		if err != nil {
			h.logger.Errorf("failed to marshal cpe, reason: %v", err)
			continue
		}
		if err := h.queue.Publish("", defs.QueueLogstashCPE, rabbit.Message{
			Body:        bts,
			ContentType: rabbit.MIMEApplicationJSON,
			Mode:        rabbit.Persistent,
		}); err != nil {
			h.logger.Errorf("failed to publish cpe, reason: %v", err)
			continue
		}
	}
}

func (h *CVEHandler) crawlAsset(ctx context.Context) {
	group, c := errgroup.WithContext(ctx)
	delay := clock.Minute * 5
	group.Go(func() error {
		for {
			allowRoles := make([]string, 0)
			roles, err := h.mongo.Account().Roles().FindAll(&bson.M{"permissions": defs.PermissionViewVul}, []string{})
			if err != nil {
				h.logger.Errorf("h.mongo.Roles.FindAll failed, reason: %v", err)
				if err.Error() != mg.NotFoundError {
					return err
				}
				clock.Sleep(delay)
				continue
			}
			for _, role := range roles {
				allowRoles = append(allowRoles, role.RoleID)
			}
			orgs, err := h.mongo.Account().GroupUser().FindAll(&bson.M{"role": bson.M{"$in": allowRoles}}, []string{})
			if err != nil {
				h.logger.Errorf("h.mongo.GroupUser.FindAll failed, reason: %v", err)
				if err.Error() != mg.NotFoundError {
					return err
				}
				clock.Sleep(delay)
				continue
			}
			organizations := make([]string, 0)
			for _, org := range orgs {
				if org.IsActive() {
					organizations = append(organizations, org.TenantID)
				}
			}
			h.logger.Infof("total: %d organizations", len(organizations))
			// Collect Asset
			query := map[string]interface{}{
				"bool": map[string]interface{}{
					"filter": []interface{}{
						map[string]interface{}{
							"term": map[string]interface{}{
								"type": defs.AssetTypeProduct,
							},
						},
						map[string]interface{}{
							"term": map[string]interface{}{
								"status": defs.AssetStatusCodeApproved,
							},
						},
						map[string]interface{}{
							"term": map[string]interface{}{
								"active": true,
							},
						},
						map[string]interface{}{
							"term": map[string]interface{}{
								"visible": true,
							},
						},
						map[string]interface{}{
							"terms": map[string]interface{}{
								"organization": organizations,
							},
						},
					},
				},
			}
			assets, err := h.elastic.Enduser().Asset().FindAll(context.Background(), query, []string{})
			if err != nil {
				h.logger.Errorf("h.elastic.Enduser.Asset.FindAll failed, reason: %v", err)
				clock.Sleep(delay)
				continue
			}
			h.logger.Infof("total: %d assets", len(assets))
			// Lock
			news := map[string][]*cpe.Item{}
			for _, asset := range assets {
				if asset.Value != "" {
					c, err := cpe.NewItemFromFormattedString(strings.ToLower(asset.Value))
					if err != nil {
						h.logger.Errorf("cpe.NewItemFromFormattedString (%s) failed, reason: %v", asset.Value, err)
						continue
					}
					if value, ok := news[asset.Organization]; ok {
						value = append(value, c)
						news[asset.Organization] = value
					} else {
						news[asset.Organization] = []*cpe.Item{c}
					}
				}
			}
			h.mutex.Lock()
			h.assets = news
			h.mutex.Unlock()
			select {
			case <-c.Done():
				return nil
			default:
				clock.Sleep(delay)
			}
		}
	})
}

func (h *CVEHandler) newPoolMultilang() *ants.PoolWithFunc {
	p, _ := ants.NewPoolWithFunc(20, func(body interface{}) {
		msg := body.(*model.CVEJobLanguage)
		defer msg.WG.Done()
		languages, err := h.elastic.Enrichment().CVELang("*").FindByID(context.Background(), msg.Results[msg.Index].ID)
		if err != nil {
			h.logger.Errorf("failed to get cve (%s) languages, reason: %v", msg.Results[msg.Index].ID, err)
			return
		}
		for _, language := range languages {
			switch language.Lang {
			case defs.LangVI:
				msg.Results[msg.Index].MultiLang.VI = language
			case defs.LangEN:
				msg.Results[msg.Index].MultiLang.EN = language
			}
		}
	})
	// Success
	return p
}

type mapChecklist struct {
	Affect    checklist
	Exploit   checklist
	Patch     checklist
	Ability   checklist
	Condition checklist
}
type checklist struct {
	Title     string       `json:"title"`
	Checklist []checkpoint `json:"checklist"`
}
type checkpoint struct {
	Point int `json:"point"`
}

func (h *CVEHandler) verifySearchReject(body model.RequestCVESearch) (err error) {
	if body.Keyword != "" {
		body.Keyword = strings.ToLower(strings.TrimSpace(body.Keyword))
	}
	if body.Checker != "" {
		body.Checker = strings.ToLower(body.Checker)
	}
	if body.Status == nil {
		body.Status = make([]int, 0)
	}
	if body.Severity.VTI.Version == "" && len(body.Severity.VTI.Value) > 0 {
		body.Severity.VTI.Version = defs.VersionVcs10
	}
	if body.Severity.VTI.Version != "" {
		if !slice.String(defs.EnumVTIVersion).Contains(body.Severity.VTI.Version) {
			return errors.New("invalid value for param <severity.vti.version>")
		}
	}
	if body.Severity.VTI.Value == nil {
		body.Severity.VTI.Value = make([]int, 0)
	}

	if body.Severity.Global.Version == nil {
		body.Severity.Global.Version = make([]string, 0)
	}

	for _, it := range body.Severity.Global.Version {
		if it != "" {
			if !slice.String(defs.EnumGlobalVersion).Contains(it) {
				return errors.New("invalid value for param <severity.global.version>")
			}
		}
	}
	if body.Severity.Global.SeverityVersion2 == nil {
		body.Severity.Global.SeverityVersion2 = make([]int, 0)
	}
	if body.Severity.Global.SeverityVersion3 == nil {
		body.Severity.Global.SeverityVersion3 = make([]int, 0)
	}
	if len(body.Languages) > 0 {
		languages := make([]string, 0)
		for _, lang := range body.Languages {
			if !strings.Contains(lang, ",") {
				if _, ok := defs.MappingLanguage[lang]; !ok {
					return errors.New("invalid value for param <languages>")
				}
			}
			languages = append(languages, lang)
		}
		body.Languages = slice.String(languages).Unique().Extract()
	}
	if body.Time.Approved.Gte > 0 && body.Time.Approved.Lte > 0 && body.Time.Approved.Gte > body.Time.Approved.Lte {
		return errors.New("invalid value for param <time.approved.gte> greater than <time.approved.lte>")
	}
	if body.Time.Modified.Gte > 0 && body.Time.Modified.Lte > 0 && body.Time.Modified.Gte > body.Time.Modified.Lte {
		return errors.New("invalid value for param <time.modified.gte> greater than <time.modified.lte>")
	}
	if body.Time.AnalysisTime.Gte > 0 && body.Time.AnalysisTime.Lte > 0 && body.Time.AnalysisTime.Gte > body.Time.AnalysisTime.Lte {
		return errors.New("invalid value for param <time.modified.gte> greater than <time.modified.lte>")
	}
	body.Sort = make([]string, 0)
	if body.Time.Approved.Gte > 0 || body.Time.Approved.Lte > 0 {
		body.Sort = append(body.Sort, "-approved")
	}

	if body.Time.Modified.Gte > 0 || body.Time.Modified.Lte > 0 {
		body.Sort = append(body.Sort, "-modified")
	}

	if body.Time.AnalysisTime.Gte > 0 || body.Time.AnalysisTime.Lte > 0 {
		body.Sort = append(body.Sort, "-analysis_time")
	}

	if len(body.Sort) == 0 || len(body.Sort) > 1 {
		body.Sort = append(body.Sort, "-analysis_time")
	}
	if body.Size == 0 {
		body.Size = 20
	}
	// Success
	return nil
}

func (h *CVEHandler) GetGroup(tenantID string) (*model.Group, error) {
	client := resty.New()
	client.SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	client.SetTimeout(time.Duration(clock.Minute * clock.Duration(h.config.Api.Timeout)))
	res, err := client.R().Get(fmt.Sprintf(h.config.Api.APIGroup, tenantID))
	if err != nil {
		return nil, errors.New(fmt.Sprintf("get url (%s) error, reason: %v", fmt.Sprintf(h.config.Api.APIGroup, tenantID), err))
	}
	if res.StatusCode() != rest.StatusOK {
		return nil, errors.New(fmt.Sprintf("get url (%s) return code %d", fmt.Sprintf(h.config.Api.APIGroup, tenantID), res.StatusCode()))
	}
	group := model.GetGroupResponse{}
	err = json.Unmarshal(res.Body(), &group)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("marshal group error: %v", err))
	}

	return &group.Detail, nil
}

func ConvertCPEFormat(cpe string) *model.CPE {
	CPEformat := strings.Split(cpe, ":")
	return &model.CPE{
		CPEDetail: model.CPEDetail{
			Value:   cpe,
			Vendor:  CPEformat[3],
			Part:    CPEformat[2],
			Product: CPEformat[4],
			Version: CPEformat[5],
			Update:  CPEformat[6],
		},
		SwEdition: CPEformat[9],
		Edition:   CPEformat[7],
		Other:     CPEformat[12],
		Language:  CPEformat[8],
		TargetSw:  CPEformat[10],
		TargetHw:  CPEformat[11],
	}
}

func validateCVSS(cvssArray []model.CVEMetric, required bool) ([]model.CVEMetric, error) {
	if required && len(cvssArray) == 0 {
		return cvssArray, errors.New("CVSS scores là bắt buộc")
	}

	if len(cvssArray) == 0 {
		return cvssArray, nil
	}

	versionPriority := utils.GetCVSSVersionPriority()
	versionMap := make(map[string]bool)

	for i, cvss := range cvssArray {
		cvss.Version = strings.TrimSpace(cvss.Version)
		if cvss.Version == "" {
			return cvssArray, fmt.Errorf("CVSS version là bắt buộc cho item %d", i+1)
		}

		if _, exists := versionPriority[cvss.Version]; !exists {
			return cvssArray, fmt.Errorf("CVSS version không được hỗ trợ: %s", cvss.Version)
		}

		if versionMap[cvss.Version] {
			return cvssArray, fmt.Errorf("CVSS version bị trùng lặp: %s", cvss.Version)
		}
		versionMap[cvss.Version] = true

		if !utils.ValidateCVSSVectorString(cvss.Version, cvss.VectorString) {
			return cvssArray, fmt.Errorf("CVSS vector string không hợp lệ cho version %s tại item %d", cvss.Version, i+1)
		}

		if cvss.Score < 0.0 || cvss.Score > 10.0 {
			return cvssArray, fmt.Errorf("CVSS score không hợp lệ cho item %d: phải từ 0.0 đến 10.0", i+1)
		}

		cvss.Score = float32(math.Round(float64(10*cvss.Score)) / 10)

		if cvss.Severity < int(defs.UnkownSeverity) || cvss.Severity > int(defs.CriticalSeverity) {
			return cvssArray, fmt.Errorf("CVSS severity không hợp lệ cho item %d: phải từ %d đến %d", i+1, int(defs.UnkownSeverity), int(defs.CriticalSeverity))
		}

		cvssArray[i] = cvss
	}

	return cvssArray, nil
}

func validateEPSS(epss model.EPSSMetric) error {
	if epss.Score > 0 || epss.Percentile > 0 {
		if epss.Score > 0 {
			if epss.Score < 0.0 || epss.Score > 1.0 {
				return errors.New("EPSS score không hợp lệ: phải từ 0.0 đến 1.0")
			}
		}

		if epss.Percentile > 0 {
			if epss.Percentile < 0.0 || epss.Percentile > 1.0 {
				return errors.New("EPSS percentile không hợp lệ: phải từ 0.0 đến 1.0")
			}
		}
	}
	return nil
}

func validateCWE(cweArray []model.CWEMetric) ([]model.CWEMetric, error) {
	if len(cweArray) == 0 {
		return cweArray, nil
	}

	for i, cwe := range cweArray {
		cwe.ID = strings.TrimSpace(cwe.ID)
		if cwe.ID == "" {
			return cweArray, fmt.Errorf("CWE ID là bắt buộc cho item %d", i+1)
		}

		if !regexp.MustCompile(defs.RegexCWEID).MatchString(cwe.ID) {
			return cweArray, fmt.Errorf("CWE ID format không hợp lệ cho item %d: phải là CWE-XXXX", i+1)
		}

		cwe.Name = strings.TrimSpace(cwe.Name)
		if cwe.Name == "" {
			return cweArray, fmt.Errorf("CWE Name là bắt buộc cho item %d", i+1)
		}

		if cwe.Link != "" {
			cwe.Link = strings.TrimSpace(cwe.Link)
			if !strings.HasPrefix(cwe.Link, "http://") && !strings.HasPrefix(cwe.Link, "https://") {
				return cweArray, fmt.Errorf("CWE Link format không hợp lệ cho item %d: phải là URL hợp lệ", i+1)
			}
		}
		cweArray[i] = cwe
	}

	cweMap := make(map[string]bool)
	for i, cwe := range cweArray {
		if cweMap[cwe.ID] {
			return cweArray, fmt.Errorf("CWE ID bị trùng lặp: %s tại item %d", cwe.ID, i+1)
		}
		cweMap[cwe.ID] = true
	}

	return cweArray, nil
}

func validateCVEID(id string) (string, error) {
	id = strings.ToUpper(id)
	re := regexp.MustCompile(defs.RegexCVE)
	if !re.MatchString(id) {
		return "", errors.New("CVE ID format không hợp lệ")
	}
	return id, nil
}

func validateLanguage(lang string, required bool, defaultLang string) (string, error) {
	lang = strings.TrimSpace(lang)

	if required && lang == "" {
		return "", errors.New("ngôn ngữ là bắt buộc")
	}

	if !required && lang == "" {
		lang = defaultLang
	}

	if _, ok := defs.MappingLanguage[lang]; !ok {
		return "", errors.New("ngôn ngữ không hợp lệ cho parameter <lang>")
	}

	return lang, nil
}
