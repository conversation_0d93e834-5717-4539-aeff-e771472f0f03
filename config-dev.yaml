app:
debug: false
address: "0.0.0.0:8934"
api_key: "$2a$10$H2dXY2l8ttRC3xg8RvRGWeW4Kpd3UCWwm$0gAUik8D6$hUPhmavMK"
max_size: 10000
number_worker: 100
api:
mail: "http://mail.ti.visc.com"
customer: "http://api.os.ti.visc.com/customer-service"
api_group: "http://api.os.ti.visc.com/customer-service/organization/%s"
timeout: 30
connector:
mongo:
account:
address: "mg-admin:vtios@localhost:27011/?directConnection=true"
auth:
enable: false
enduser:
address: "mg-admin:vtios@localhost:27012/?directConnection=true"
auth:
enable: false
elastic:
enduser:
version: 7
scheme: "http"
address: "http://localhost:9220"
auth:
enable: false
username: ""
password: ""
enrichment:
version: 7
scheme: "http"
address: "http://localhost:9211"
auth:
enable: false
username: ""
password: ""
rabbit:
crawler:
secure: false
address: "localhost:5172"
username: "ti-crawler"
password: "vtios"
redis:
general:
address: "localhost:6379"
password: "vtios"
db: 13
kafka:
topics:
udm_enrichment_topic: "udm-enrichment-topic"
udm_evaluate_topic: "udm-evaluate-topic"
producers:
enrichment:
brokers: "localhost:9092"
auth:
enable: false
mechanism: ""
username: ""
password: ""
tls:
enable: false
insecure_skip_verify: false
cert_file: ""
key_file: ""
ca_file: ""
async: true
evaluate:
brokers: "localhost:9092"
auth:
enable: false
mechanism: ""
username: ""
password: ""
tls:
enable: false
insecure_skip_verify: false
cert_file: ""
key_file: ""
ca_file: ""
async: true
threat_feed:
brokers: "localhost:9092"
auth:
enable: false
mechanism: ""
username: ""
password: ""
tls:
enable: false
insecure_skip_verify: false
cert_file: ""
key_file: ""
ca_file: ""
async: true