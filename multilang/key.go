package multilang

const (
	// Title
	KeyTitleTTIAlert            = "title_tti_alert"
	KeyTitleTTISupport          = "title_tti_support"
	KeyTitleThreatMalware       = "title_threat_malware"
	KeyTitleThreatVulnerability = "title_threat_vulnerability"
	KeyTitleThreatBrandAbuse    = "title_threat_brandabuse"
	// Sheet
	KeySheetTTIAlert            = "sheet_tti_alert"
	KeySheetTTISupport          = "sheet_tti_support"
	KeySheetThreatMalware       = "sheet_threat_malware"
	KeySheetThreatVulnerability = "sheet_threat_vulnerability"
	KeySheetThreatBrandAbuse    = "sheet_threat_brandabuse"
	// Common
	KeySummary     = "summary"
	KeySeverity    = "severity"
	KeyCritical    = "critical"
	KeyHigh        = "high"
	KeyMedium      = "medium"
	KeyLow         = "low"
	KeyStatus      = "status"
	KeyPass        = "pass"
	KeyFail        = "fail"
	KeyPending     = "pending"
	KeyInprogress  = "inprogress"
	KeyDone        = "done"
	KeyReject      = "reject"
	KeyObject      = "object"
	KeyNo          = "no"
	KeyTime        = "time"
	KeyCategory    = "category"
	KeyTitle       = "title"
	KeyMessage     = "message"
	KeyDescription = "description"
	// File TTI-Report.xlsx
	KeyTitleListAlerts = "title_list_alerts"
	KeyLinkAlert       = "link_alert"
	KeyRequestTime     = "request_time"
	KeyDoneTime        = "done_time"
	KeyProcessTime     = "process_time"
	KeyRequestType     = "request_type"
	KeyRequestProcess  = "request_process"
	KeyProcessTimeData = "process_time_data"
	// File Threat-Report.xlsx
	KeyAppearTime       = "appear_time"
	KeyPermission       = "permission"
	KeyTitleListDomains = "title_list_domains"
	KeyDomain           = "domain"
	KeyType             = "type"
	KeyPackageType      = "package_type"
	Alert
	KeyTitlePhishingOne              = "title_alert_phishing_one"
	KeyTitleImpersonateOne           = "title_alert_impersonate_one"
	KeyTitleImpersonateSocialOne     = "title_alert_impersonate_social_one"
	KeyTitleLeakOne                  = "title_alert_leak_one"
	KeyTitleTargetedVulnerabilityOne = "title_alert_targeted_vulnerability_one"
	KeySuffixDateGte                 = "suffix_date_gte"
	KeySuffixDateLte                 = "suffix_date_lte"
	KeySuffixDate                    = "suffix_date"
)
